import os
import json
from datetime import datetime

from .validation_helper.validation_execution import ValidatorExecution
from ..common.aria_helper.boto3_utils import get_secret, trigger_lambda_response
from ..common.aria_helper.mongo_utils import Mongo
from ..common.aria_helper.aria_utils import ARIA

environment = os.environ['ENV']
database_name = os.environ['DATABASE_NAME']
aria_environment = os.environ['ARIA_ENVIRONMENT']
common_prefix = f"{os.environ['ENV']}-{os.environ['PROJECT_NAME']}"


class BreValidationHandler:
    def __init__(self, event):
        self.event = event
        # Parse the event body once and reuse it
        self.document = event['body'] if isinstance(event['body'], dict) else json.loads(event['body'])
        self.input_body = self.document
        self.app_id = self.document['app_id']
        self.document_id = self.document['id']
        self.current_status_id = self.document.get('aria_status', '')
        self.statuses = self.input_body.get('aria_status', self.current_status_id)
        self.ocr_groups = self.document.get('ocr_groups', [])
        self.request_response = self.input_body.get('request_response', False)

        self.mongo_client = Mongo(get_secret(common_prefix + '-mongodb_uri', return_json=False)).client
        self.validator_execution = ValidatorExecution()
        self.aria_secret = get_secret(secret_name=f'dev-Connector-aria_creds')
        self.validation_config = self.mongo_client[database_name]["validation_config"].find_one({
            # "app_id": self.app_id,
            "validation_type_key": "tag_titles"
        })

        if not self.validation_config:
            raise ValueError(f"No validation config found for app_id: {self.app_id}")

        if not self.app_id:
            raise ValueError("app_id is missing from the document")
    def post_to_aria(self, bre_response):
        """
        Post the BRE response to ARIA.
        Returns True if successful, raises exception if failed.
        Following working lambda pattern - always post to ARIA
        """
        try:
            # Debug: Print the aria_secret structure to understand what environments are available
            print("ARIA Secret keys:", list(self.aria_secret.keys()) if isinstance(self.aria_secret, dict) else "Not a dict")
            print("ARIA Environment variable:", aria_environment)

            # Try to use the aria_environment first, but fallback to 'sandbox' if it doesn't exist
            if isinstance(self.aria_secret, dict):
                if aria_environment in self.aria_secret:
                    aria_config = self.aria_secret[aria_environment]
                    print(f"Using ARIA environment: {aria_environment}")
                elif 'sandbox' in self.aria_secret:
                    aria_config = self.aria_secret['sandbox']
                    print("Using ARIA environment: sandbox (fallback)")
                else:
                    # Use the first available environment
                    first_key = list(self.aria_secret.keys())[0]
                    aria_config = self.aria_secret[first_key]
                    print(f"Using ARIA environment: {first_key} (first available)")
            else:
                raise ValueError("ARIA secret is not in expected dictionary format")

            aria = ARIA(
                base_url=aria_config['url'],
                request_token=aria_config['token']
            )

            print(f"Posting to ARIA - App ID: {self.app_id}, Item ID: {self.document_id}")
            print(f"BRE Response: {bre_response}")

            aria.bre_reply(
                app_id=self.app_id,
                item_id=self.document_id,
                bre_response=bre_response
            )
            print("Successfully posted to ARIA")
            return True
        except Exception as e:
            print(f"Failed to post to ARIA: {str(e)}")
            print(f"ARIA secret structure: {self.aria_secret}")
            raise

    def run(self):
        try:
            # Perform validation
            print("Performing validation")
            print("document--------" ,self.document)
            print(self.validation_config)
            is_valid, validation_result = self.validator_execution.validate_data(self.document, self.validation_config)

            # Determine target status and exception based on validation result (following old lambda pattern)
            print("Validation result------------------", self.statuses)
            if is_valid:
                # If validation passes, use the current status (or could be configured to move to next status)
                next_status_id = self.statuses  # Keep current status for now
                aria_exception = ""
            else:
                # If validation fails, keep current status but set exception
                next_status_id = self.statuses  # Keep current status
                # Build exception message from validation errors (following old lambda pattern)
                validation_errors = validation_result.get('errors', [])
                aria_exception = f"Validation failed: {'; '.join(validation_errors)}" if validation_errors else "One or more fields require human intervention"

            # Build BRE response following bre_post lambda pattern
            bre_response = {
                "aria_status": {"value": next_status_id}
            }

            # Only add aria_exception if there is one
            if aria_exception:
                bre_response["aria_exception"] = {"value": aria_exception}

            # Include the complete validation result (following bre_post lambda pattern)
            if validation_result and "groups" in validation_result:
                # Add the complete groups data with field values and exceptions as JSON string
                bre_response["groups"] = {"value": json.dumps(validation_result["groups"])}

            # Add group-level exceptions if present
            if validation_result and "group_top_exceptions" in validation_result:
                bre_response["group_top_exceptions"] = {"value": json.dumps(validation_result["group_top_exceptions"])}

            # Add validation metadata
            bre_response["validation_details"] = {"value": json.dumps({
                "validation_passed": is_valid,
                "processed_at": json.dumps({"$date": datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%fZ')})
            })}

            print("REQUEST RESPONSE", self.request_response)
            print("********* TO STATE ", next_status_id)

            self.post_to_aria(bre_response)
            print("bre_response-------------", bre_response)

            # Build the complete response body including all data from the original document
            response_body = dict(self.document)  # Start with all original document data

            # Update the aria fields
            response_body["aria_status"] = next_status_id
            response_body["aria_exception"] = aria_exception

            # Update the groups data with bre_exceptions from validation
            if validation_result and "groups" in validation_result:
                for group_name, group_data in validation_result["groups"].items():
                    if group_name in response_body.get("groups", {}):
                        # Update the existing group with validation results
                        response_body["groups"][group_name].update(group_data)
                    else:
                        # Add new group if it doesn't exist
                        if "groups" not in response_body:
                            response_body["groups"] = {}
                        response_body["groups"][group_name] = group_data

            # Add group_top_exceptions if present
            if validation_result and "group_top_exceptions" in validation_result:
                response_body["group_top_exceptions"] = validation_result["group_top_exceptions"]

            # Return response in the wanted format
            return {
                'statusCode': 200,
                'body': response_body
            }

        except Exception as e:
            print('Error: {}'.format(str(e)))
            return {
                'statusCode': 500,
                'body': json.dumps({'error': str(e)})
            }


def lambda_handler(event, context=None):
    print("event-data",event)
    if 'body' not in list(event.keys()):
        raise ValueError("body tag is missing on the dict. Skipping...")

    bre_validation_handler = BreValidationHandler(event)
    return bre_validation_handler.run()
